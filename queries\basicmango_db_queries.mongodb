// Student Database Queries - OR, NOT, NOR operators
// Press Ctrl+Shift+R to run

use('basicmango_db');

// QUERY 29: Display Records Where Gender Is Female or Age >=20, Use OR Operator
db.students.find({
  $or: [
    { Gender: "Female" },
    { age: { $gte: 20 } }
  ]
});

// Got 3 results - all the female students (no one is 20+ years old)
// <PERSON><PERSON><PERSON>, <PERSON><PERSON>, Bhomika

// QUERY 30: Display Records Where grd_point >=36, or Gender=Male, Use OR Operator
db.students.find({
  $or: [
    { grd_point: { $gte: 36 } },
    { Gender: "Male" }
  ]
});

// Found 4 students: 3 males + <PERSON><PERSON> (she has 36.25 grade points)

// QUERY 31: Demonstrate NOT Operator ie Opposite of Age>=15
db.students.find({
  age: { $not: { $gte: 15 } }
});

// This gives us the younger kids: <PERSON><PERSON><PERSON> (11), <PERSON><PERSON><PERSON> (13), <PERSON><PERSON><PERSON> (14)

// Query 32: Find non-female students (basically the boys)
db.students.find({
  Gender: { $ne: "Female" }
});

// Results: <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON> - all the male students

// Another way to write the same thing
db.students.find({
  Gender: { $not: { $eq: "Female" } }
});

// Query 33: NOR operator - NOT female AND NOT 15+
db.students.find({
  $nor: [
    { Gender: "Female" },
    { age: { $gte: 15 } }
  ]
});

// Only got Mukesh and Akash - young boys under 15

// Query 34: Exclude students aged 11 or 16
db.students.find({
  age: { $nin: [16, 11] }
});

// Got everyone except Mukesh (11) and Nitin (16)

// Same thing using $not
db.students.find({
  age: { $not: { $in: [16, 11] } }
});

// Query 35: Everyone except 11-year-olds
db.students.find({
  age: { $ne: 11 }
});

// Got 5 students - everyone except Mukesh

// Different way to write it
db.students.find({
  age: { $not: { $eq: 11 } }
});

// Query 36: NOT less than 29 grade points (so 29 or higher)
db.students.find({
  grd_point: { $not: { $lt: 29 } }
});

// Got all 6 students - everyone has decent grades (30 is lowest)

// Easier way to write this
db.students.find({
  grd_point: { $gte: 29 }
});

// Some quick checks:

// Total students
db.students.countDocuments({});

// How many boys vs girls?
db.students.countDocuments({ Gender: "Male" });
db.students.countDocuments({ Gender: "Female" });

// Show everyone sorted by ID
db.students.find({}).sort({ _id: 1 });