// Assignment Queries - Device Database
// Run with Ctrl+Shift+R

use('inter');

// Query 16: Check if price field exists and show name + price
db.devices.find(
  { price: { $exists: true } },
  { name: 1, price: 1, _id: 0 }
);

// Results: Got all 5 devices with their prices
// IPhone: 799, xTablet: 899, <PERSON><PERSON> Note: 899, Samsung: 699, Vivo: 599

// Alternative way - same result
db.devices.find(
  { price: { $ne: null } },
  { name: 1, price: 1, _id: 0 }
);

// Query 17: Look for profit field (should be empty)
db.devices.find(
  { profit: { $exists: true } }
);

// Result: Empty array [] - no profit field in any document

// Just checking - show docs that DON'T have profit field
db.devices.find(
  { profit: { $exists: false } },
  { name: 1, _id: 0 }
);

// Query 18: Find devices with price > 699
// First try with both conditions
db.devices.find({
  $and: [
    { price: { $exists: true } },
    { price: { $gt: 699 } }
  ]
});

// Simpler version - $gt already checks if field exists
db.devices.find(
  { price: { $gt: 699 } }
);

// Got 3 devices: IPhone (799), xTablet (899), <PERSON><PERSON> Note (899)
// Samsung is exactly 699 so not included, Vivo is 599

// Show just names and prices for the expensive ones
db.devices.find(
  { price: { $gt: 699 } },
  { name: 1, price: 1, _id: 0 }
);

// Some extra checks I did:

// How many have price field? (should be 5)
db.devices.countDocuments({ price: { $exists: true } });

// How many have profit field? (should be 0)
db.devices.countDocuments({ profit: { $exists: true } });

// Show expensive devices with all details
db.devices.find({ price: { $gt: 699 } }).pretty();
