// Assignment Queries - Device Database
// Run with Ctrl+Shift+R

use('inter');

// QUERY 16: Find Price Field Exists In Document, If Exists Display Name And Price
db.devices.find(
  { price: { $exists: true } },
  { name: 1, price: 1, _id: 0 }
);

// Results: Got all 5 devices with their prices
// IPhone: 799, xTablet: 899, Redmi Note: 899, Samsung: 699, Vivo: 599

// Alternative way - same result
db.devices.find(
  { price: { $ne: null } },
  { name: 1, price: 1, _id: 0 }
);

// QUERY 17: Check If Profit Field Exists, It Does Not Exist, Hence Output Will Be [] Blank
db.devices.find(
  { profit: { $exists: true } }
);

// Result: Empty array [] - no profit field in any document

// Just checking - show docs that DON'T have profit field
db.devices.find(
  { profit: { $exists: false } },
  { name: 1, _id: 0 }
);

// QUERY 18: Find price field exists in document and price > 699
db.devices.find({
  $and: [
    { price: { $exists: true } },
    { price: { $gt: 699 } }
  ]
});

// Simpler version - $gt already checks if field exists
db.devices.find(
  { price: { $gt: 699 } }
);

// Got 3 devices: IPhone (799), xTablet (899), Redmi Note (899)
// Samsung is exactly 699 so not included, Vivo is 599

// Show just names and prices for the expensive ones
db.devices.find(
  { price: { $gt: 699 } },
  { name: 1, price: 1, _id: 0 }
);
