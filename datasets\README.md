# Datasets Directory

This directory is for storing your MongoDB datasets.

## Supported Formats
- **JSON files** (.json) - Can be imported directly into MongoDB
- **CSV files** (.csv) - Can be imported using MongoDB Compass or mongoimport
- **BSON files** (.bson) - MongoDB native format

## How to Import Data

### Using MongoDB Compass
1. Open MongoDB Compass
2. Connect to your database
3. Select or create a database
4. Select or create a collection
5. Click "ADD DATA" → "Import File"
6. Choose your JSON/CSV file
7. Configure import settings
8. Click "Import"

### Using MongoDB for VS Code
1. Open Command Palette (Ctrl+Shift+P)
2. Type "MongoDB: Launch MongoDB Shell"
3. Use mongoimport command:
   ```bash
   mongoimport --db your_database --collection your_collection --file dataset.json --jsonArray
   ```

## File Naming Convention
- Use descriptive names: `products_data.json`, `users_sample.csv`
- Include date if relevant: `sales_2024_01.json`
- Use underscores instead of spaces

## Notes
- Place your dataset files here after receiving them
- Large files (>100MB) should be compressed
- Always backup your data before importing
