// MongoDB Playground
// Use Ctrl+Shift+R (or Cmd+Shift+R on Mac) to run queries

// Select the database to use
use('your_database_name');

// Sample queries - replace with your actual queries once you have your dataset

// 1. Find all documents in a collection
db.your_collection.find({});

// 2. Find documents with specific criteria
db.your_collection.find({ field: "value" });

// 3. Count documents
db.your_collection.countDocuments({});

// 4. Aggregation pipeline example
db.your_collection.aggregate([
  { $match: { field: "value" } },
  { $group: { _id: "$category", count: { $sum: 1 } } },
  { $sort: { count: -1 } }
]);

// 5. Insert a document
db.your_collection.insertOne({
  field1: "value1",
  field2: "value2",
  createdAt: new Date()
});

// 6. Update documents
db.your_collection.updateMany(
  { field: "old_value" },
  { $set: { field: "new_value" } }
);

// 7. Delete documents
db.your_collection.deleteMany({ field: "value_to_delete" });
