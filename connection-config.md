# MongoDB Connection Configuration

## For MongoDB Compass
Your MongoDB Compass should already be configured. If you need to add a new connection:

1. Open MongoDB Compass
2. Click "New Connection"
3. Enter your connection string or fill in the connection form:
   - **Hostname**: localhost (for local) or your server address
   - **Port**: 27017 (default)
   - **Authentication**: If required
   - **Database**: Optional default database

## For VS Code MongoDB Extension
1. Install the MongoDB for VS Code extension
2. Open Command Palette (Ctrl+Shift+P)
3. Type "MongoDB: Connect"
4. Use the same connection string as your Compass connection

## Common Connection Strings
```
# Local MongoDB (default)
mongodb://localhost:27017

# Local MongoDB with authentication
*******************************************

# MongoDB Atlas
mongodb+srv://username:<EMAIL>/database_name

# Remote MongoDB server
**************************************************************
```

## Environment Variables (Optional)
Create a `.env` file for storing connection strings securely:
```
MONGODB_URI=mongodb://localhost:27017
DATABASE_NAME=your_database_name
```

**Note**: Add `.env` to `.gitignore` to keep credentials secure!
