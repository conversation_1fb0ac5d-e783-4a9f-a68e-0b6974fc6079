# MongoDB Assignment - Queries 16, 17, 18

## Dataset Information
- **File**: `datasets/devices_data.json`
- **Collection**: `devices`
- **Database**: `devices_db`
- **Documents**: 5 device records (IPhone, xTablet, Redmi Note, Samsung, Vivo)

## Step 1: Import Dataset

### Option A: Using MongoDB Compass
1. Open MongoDB Compass
2. Connect to your MongoDB instance
3. Create database: `devices_db`
4. Create collection: `devices`
5. Click "ADD DATA" → "Import File"
6. Select `datasets/devices_data.json`
7. Choose "JSON" format
8. Click "Import"

### Option B: Using MongoDB Shell/Command Line
```bash
mongoimport --db devices_db --collection devices --file datasets/devices_data.json --jsonArray
```

## Step 2: Run Queries

### Using VS Code MongoDB Extension
1. Open `queries/assignment-queries.mongodb`
2. Select the query you want to run
3. Press `Ctrl+Shift+R` (or `Cmd+Shift+R` on Mac)

### Using MongoDB Compass
1. Go to `devices_db` → `devices` collection
2. Click on "Aggregations" or use the query bar
3. Copy and paste the queries from the assignment file

## Expected Results

### Query 16: Find Price Field Exists, Display Name And Price
```json
[
  { "name": "IPhone", "price": 799 },
  { "name": "xTablet", "price": 899 },
  { "name": "Redmi Note", "price": 899 },
  { "name": "Samsung", "price": 699 },
  { "name": "Vivo", "price": 599 }
]
```

### Query 17: Check If Profit Field Exists
```json
[]
```
(Empty array because no documents have 'profit' field)

### Query 18: Price exists and price > 699
```json
[
  {
    "_id": 1,
    "name": "IPhone",
    "price": 799,
    "releaseDate": "2011-05-14T00:00:00Z",
    "spec": { "ram": 4, "screen": 6.5, "cpu": 2.66 },
    "color": ["white", "black"],
    "storage": [64, 128, 256]
  },
  {
    "_id": 2,
    "name": "xTablet",
    "price": 899,
    "releaseDate": "2011-09-01T00:00:00Z",
    "spec": { "ram": 16, "screen": 9.5, "cpu": 3.66 },
    "color": ["white", "black", "purple"],
    "storage": [128, 256, 512]
  },
  {
    "_id": 3,
    "name": "Redmi Note",
    "price": 899,
    "releaseDate": "2015-01-14T00:00:00Z",
    "spec": { "ram": 12, "screen": 9.7, "cpu": 3.66 },
    "color": ["blue"],
    "storage": [16, 64, 128]
  }
]
```

## Query Explanations

### Query 16: `{ price: { $exists: true } }`
- **Purpose**: Find documents where 'price' field exists
- **Projection**: `{ name: 1, price: 1, _id: 0 }` shows only name and price
- **Result**: All 5 devices (all have price field)

### Query 17: `{ profit: { $exists: true } }`
- **Purpose**: Find documents where 'profit' field exists
- **Result**: Empty array [] because no documents have 'profit' field

### Query 18: `{ price: { $gt: 699 } }`
- **Purpose**: Find documents where price exists AND price > 699
- **Result**: 3 devices (IPhone: 799, xTablet: 899, Redmi Note: 899)
- **Note**: Samsung (699) is excluded because 699 is not > 699
