# 🚀 Basic Mango MongoDB Shell Commands

## 📋 Step-by-Step MongoDB Shell Execution

### **Step 1: Import Dataset**
```bash
# Run this in command prompt/terminal (NOT in mongo shell)
mongoimport --db basicmango_db --collection students --file "datasets/basicmango.json" --jsonArray
```

### **Step 2: Start MongoDB Shell**
```bash
mongosh
# or for older versions:
mongo
```

### **Step 3: Connect to Database**
```javascript
use basicmango_db
```

### **Step 4: Verify Data Import**
```javascript
// Check document count
db.students.countDocuments()
// Should return: 6

// View all data
db.students.find({}).pretty()
```

## 🎯 **Assignment Queries (29-36)**

### **Query 29: OR Operator - Gender Female OR Age >=20**
```javascript
db.students.find({
  $or: [
    { Gender: "Female" },
    { age: { $gte: 20 } }
  ]
})
```
**Expected**: 3 documents (all female students)

### **Query 30: OR Operator - grd_point >=36 OR Gender=Male**
```javascript
db.students.find({
  $or: [
    { grd_point: { $gte: 36 } },
    { Gender: "Male" }
  ]
})
```
**Expected**: 4 documents (3 males + 1 female with high grade)

### **Query 31: NOT Operator - Opposite of Age>=15**
```javascript
db.students.find({
  age: { $not: { $gte: 15 } }
})
```
**Expected**: 3 documents (students under 15)

### **Query 32: NOT Operator - Gender Not Equal To Female**
```javascript
db.students.find({
  Gender: { $ne: "Female" }
})
```
**Expected**: 3 documents (all male students)

### **Query 33: NOR Operator**
```javascript
db.students.find({
  $nor: [
    { Gender: "Female" },
    { age: { $gte: 15 } }
  ]
})
```
**Expected**: 2 documents (males under 15)

### **Query 34: NOT with IN Operator - Age Not In [16,11]**
```javascript
db.students.find({
  age: { $nin: [16, 11] }
})
```
**Expected**: 4 documents (excluding ages 11 and 16)

### **Query 35: NOT with IN Operator - Age != 11**
```javascript
db.students.find({
  age: { $ne: 11 }
})
```
**Expected**: 5 documents (all except Mukesh)

### **Query 36: NOT with Less Than - !(grd_point < 29)**
```javascript
db.students.find({
  grd_point: { $not: { $lt: 29 } }
})
```
**Expected**: 6 documents (all students have grd_point >= 29)

## 🔧 **Verification Commands**

```javascript
// Count by gender
db.students.countDocuments({ Gender: "Male" })    // Should return: 3
db.students.countDocuments({ Gender: "Female" })  // Should return: 3

// Age statistics
db.students.find({}, { std_name: 1, age: 1, _id: 0 }).sort({ age: 1 })

// Grade point statistics
db.students.find({}, { std_name: 1, grd_point: 1, _id: 0 }).sort({ grd_point: 1 })

// Show all data sorted by ID
db.students.find({}).sort({ _id: 1 })
```

## 📊 **Expected Results Summary**

| Student | Gender | Age | Grade Point | Class |
|---------|--------|-----|-------------|-------|
| Mukesh | Male | 11 | 33 | VI |
| Dechamma | Female | 13 | 30 | VI |
| Akash | Male | 14 | 35.1257 | V |
| Geetha | Female | 17 | 36.2514 | X |
| Bhomika | Female | 19 | 35.5201 | X |
| Nitin | Male | 16 | 35.5201 | V |

## 🎯 **Quick Test Commands**

```javascript
// Test OR operator
db.students.find({ $or: [{ Gender: "Female" }, { age: { $gte: 20 } }] }).count()

// Test NOT operator
db.students.find({ age: { $not: { $gte: 15 } } }).count()

// Test NOR operator
db.students.find({ $nor: [{ Gender: "Female" }, { age: { $gte: 15 } }] }).count()

// Test NIN operator
db.students.find({ age: { $nin: [16, 11] } }).count()
```

## 🚪 **Exit Shell**
```javascript
exit
```

**All queries will produce the exact outputs shown in the `basicmango-results.md` file!** 🎉
