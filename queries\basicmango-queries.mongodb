// MongoDB Playground - Basic Mango Queries (OR, NOT, NOR Operators)
// Use Ctrl+Shift+R (or Cmd+Shift+R on Mac) to run queries

// Select the database to use
use('basicmango_db');

// ========================================
// BASIC MANGO QUERIES - OR, NOT, NOR OPERATORS
// ========================================

// ========================================
// QUERY 29: Display Records Where Gender Is Female or Age >=20, Use OR Operator
// ========================================

db.students.find({
  $or: [
    { Gender: "Female" },
    { age: { $gte: 20 } }
  ]
});

/* OUTPUT:
[
  {
    "_id": 2,
    "std_name": "Dechamma",
    "Gender": "Female",
    "class": "VI",
    "age": 13,
    "grd_point": 30
  },
  {
    "_id": 4,
    "std_name": "Geetha",
    "Gender": "Female",
    "class": "X",
    "age": 17,
    "grd_point": 36.2514
  },
  {
    "_id": 5,
    "std_name": "Bhomika",
    "Gender": "Female",
    "class": "X",
    "age": 19,
    "grd_point": 35.5201
  }
]
*/

// ========================================
// QUERY 30: Display Records Where grd_point >=36, or Gender=Male, Use OR Operator
// ========================================

db.students.find({
  $or: [
    { grd_point: { $gte: 36 } },
    { Gender: "Male" }
  ]
});

/* OUTPUT:
[
  {
    "_id": 1,
    "std_name": "Mukesh",
    "Gender": "Male",
    "class": "VI",
    "age": 11,
    "grd_point": 33
  },
  {
    "_id": 3,
    "std_name": "Akash",
    "Gender": "Male",
    "class": "V",
    "age": 14,
    "grd_point": 35.1257
  },
  {
    "_id": 4,
    "std_name": "Geetha",
    "Gender": "Female",
    "class": "X",
    "age": 17,
    "grd_point": 36.2514
  },
  {
    "_id": 6,
    "std_name": "Nitin",
    "Gender": "Male",
    "class": "V",
    "age": 16,
    "grd_point": 35.5201
  }
]
*/

// ========================================
// QUERY 31: Demonstrate NOT Operator ie Opposite of Age>=15
// ========================================

db.students.find({
  age: { $not: { $gte: 15 } }
});

/* OUTPUT:
[
  {
    "_id": 1,
    "std_name": "Mukesh",
    "Gender": "Male",
    "class": "VI",
    "age": 11,
    "grd_point": 33
  },
  {
    "_id": 2,
    "std_name": "Dechamma",
    "Gender": "Female",
    "class": "VI",
    "age": 13,
    "grd_point": 30
  },
  {
    "_id": 3,
    "std_name": "Akash",
    "Gender": "Male",
    "class": "V",
    "age": 14,
    "grd_point": 35.1257
  }
]
*/

// Alternative method using $lt (less than 15)
db.students.find({
  age: { $lt: 15 }
});

// ========================================
// QUERY 32: Demonstrate Not Operator. Display Records Where Gender Not Equal To Female
// ========================================

db.students.find({
  Gender: { $ne: "Female" }
});

/* OUTPUT:
[
  {
    "_id": 1,
    "std_name": "Mukesh",
    "Gender": "Male",
    "class": "VI",
    "age": 11,
    "grd_point": 33
  },
  {
    "_id": 3,
    "std_name": "Akash",
    "Gender": "Male",
    "class": "V",
    "age": 14,
    "grd_point": 35.1257
  },
  {
    "_id": 6,
    "std_name": "Nitin",
    "Gender": "Male",
    "class": "V",
    "age": 16,
    "grd_point": 35.5201
  }
]
*/

// Alternative using $not operator
db.students.find({
  Gender: { $not: { $eq: "Female" } }
});

// ========================================
// QUERY 33: Demonstrate NOR Operator
// ========================================

db.students.find({
  $nor: [
    { Gender: "Female" },
    { age: { $gte: 15 } }
  ]
});

/* OUTPUT:
[
  {
    "_id": 1,
    "std_name": "Mukesh",
    "Gender": "Male",
    "class": "VI",
    "age": 11,
    "grd_point": 33
  },
  {
    "_id": 3,
    "std_name": "Akash",
    "Gender": "Male",
    "class": "V",
    "age": 14,
    "grd_point": 35.1257
  }
]
*/

// ========================================
// QUERY 34: Demonstrate Not Operator Along with In Operator --Age Not In [16,11]
// ========================================

db.students.find({
  age: { $nin: [16, 11] }
});

/* OUTPUT:
[
  {
    "_id": 2,
    "std_name": "Dechamma",
    "Gender": "Female",
    "class": "VI",
    "age": 13,
    "grd_point": 30
  },
  {
    "_id": 3,
    "std_name": "Akash",
    "Gender": "Male",
    "class": "V",
    "age": 14,
    "grd_point": 35.1257
  },
  {
    "_id": 4,
    "std_name": "Geetha",
    "Gender": "Female",
    "class": "X",
    "age": 17,
    "grd_point": 36.2514
  },
  {
    "_id": 5,
    "std_name": "Bhomika",
    "Gender": "Female",
    "class": "X",
    "age": 19,
    "grd_point": 35.5201
  }
]
*/

// Alternative using $not with $in
db.students.find({
  age: { $not: { $in: [16, 11] } }
});

// ========================================
// QUERY 35: Demonstrate Not Operator Along With In Operator ie Age != 11
// ========================================

db.students.find({
  age: { $ne: 11 }
});

/* OUTPUT:
[
  {
    "_id": 2,
    "std_name": "Dechamma",
    "Gender": "Female",
    "class": "VI",
    "age": 13,
    "grd_point": 30
  },
  {
    "_id": 3,
    "std_name": "Akash",
    "Gender": "Male",
    "class": "V",
    "age": 14,
    "grd_point": 35.1257
  },
  {
    "_id": 4,
    "std_name": "Geetha",
    "Gender": "Female",
    "class": "X",
    "age": 17,
    "grd_point": 36.2514
  },
  {
    "_id": 5,
    "std_name": "Bhomika",
    "Gender": "Female",
    "class": "X",
    "age": 19,
    "grd_point": 35.5201
  },
  {
    "_id": 6,
    "std_name": "Nitin",
    "Gender": "Male",
    "class": "V",
    "age": 16,
    "grd_point": 35.5201
  }
]
*/

// Alternative using $not with $eq
db.students.find({
  age: { $not: { $eq: 11 } }
});

// ========================================
// QUERY 36: Demonstrate Not Operator Along With Less Than Operator –(! grd_point < 29)
// ========================================

db.students.find({
  grd_point: { $not: { $lt: 29 } }
});

/* OUTPUT:
[
  {
    "_id": 1,
    "std_name": "Mukesh",
    "Gender": "Male",
    "class": "VI",
    "age": 11,
    "grd_point": 33
  },
  {
    "_id": 2,
    "std_name": "Dechamma",
    "Gender": "Female",
    "class": "VI",
    "age": 13,
    "grd_point": 30
  },
  {
    "_id": 3,
    "std_name": "Akash",
    "Gender": "Male",
    "class": "V",
    "age": 14,
    "grd_point": 35.1257
  },
  {
    "_id": 4,
    "std_name": "Geetha",
    "Gender": "Female",
    "class": "X",
    "age": 17,
    "grd_point": 36.2514
  },
  {
    "_id": 5,
    "std_name": "Bhomika",
    "Gender": "Female",
    "class": "X",
    "age": 19,
    "grd_point": 35.5201
  },
  {
    "_id": 6,
    "std_name": "Nitin",
    "Gender": "Male",
    "class": "V",
    "age": 16,
    "grd_point": 35.5201
  }
]
*/

// Alternative using $gte (greater than or equal to 29)
db.students.find({
  grd_point: { $gte: 29 }
});

// ========================================
// ADDITIONAL VERIFICATION QUERIES
// ========================================

// Count total documents
db.students.countDocuments({});

// Count by gender
db.students.countDocuments({ Gender: "Male" });
db.students.countDocuments({ Gender: "Female" });

// Show all data for reference
db.students.find({}).sort({ _id: 1 });