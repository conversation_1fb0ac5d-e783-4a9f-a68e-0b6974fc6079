// MongoDB Playground - Basic Mango Queries
// Use Ctrl+Shift+R (or Cmd+Shift+R on Mac) to run queries

// Select the database to use
use('basicmango_db');

// ========================================
// BASIC MANGO QUERIES - PART 2
// ========================================

// Placeholder for your queries
// Please provide your queries and I will add them here with outputs

// Example structure:
// Query 1: [Your query description]
// db.collection.find({...});

// Query 2: [Your query description] 
// db.collection.find({...});

// etc.
