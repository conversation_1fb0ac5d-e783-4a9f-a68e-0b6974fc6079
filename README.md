# MANGO MongoDB Project

## Setup Instructions

### 1. Install MongoDB for VS Code Extension
1. Open VS Code
2. Go to Extensions (Ctrl+Shift+X)
3. Search for "MongoDB for VS Code"
4. Install the official MongoDB extension by MongoDB

### 2. MongoDB Connection Options

#### Option A: MongoDB Atlas (Cloud - Recommended for beginners)
1. Go to https://www.mongodb.com/atlas
2. Create a free account
3. Create a new cluster
4. Get your connection string

#### Option B: Local MongoDB Installation
1. Download MongoDB Community Server from https://www.mongodb.com/try/download/community
2. Install MongoDB locally
3. Start MongoDB service

### 3. Connect to MongoDB in VS Code
1. Open Command Palette (Ctrl+Shift+P)
2. Type "MongoDB: Connect"
3. Enter your connection string:
   - Atlas: `mongodb+srv://username:<EMAIL>/`
   - Local: `mongodb://localhost:27017`

### 4. Project Structure
```
MANGO PROJECT/
├── queries/           # MongoDB queries and aggregations
├── datasets/          # Sample data files
├── playgrounds/       # MongoDB playgrounds (.mongodb files)
├── schemas/           # Collection schemas and validation
└── README.md
```

### 5. MongoDB Playground Files
- Create `.mongodb` files for running queries
- Use MongoDB syntax directly in VS Code
- Execute queries with Ctrl+Shift+R (or Cmd+Shift+R on Mac)

## Next Steps
1. Install the MongoDB extension
2. Set up your MongoDB connection
3. Create your first playground file
4. Import your dataset
5. Start writing queries!
