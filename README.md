# MANGO MongoDB Project

## Setup Instructions

### 1. MongoDB Tools Available
✅ **MongoDB Compass** - Already installed (Visual database management)
🔄 **MongoDB for VS Code Extension** - Optional (For writing queries in VS Code)

### 2. Install MongoDB for VS Code Extension (Optional but Recommended)
1. Open VS Code
2. Go to Extensions (Ctrl+Shift+X)
3. Search for "MongoDB for VS Code"
4. Install the official MongoDB extension by MongoDB

### 3. MongoDB Connection
Since you have MongoDB Compass, you can:
- Use Compass for visual database exploration and management
- Use the same connection string in VS Code extension for query development

**Connection Options:**
- **Local MongoDB**: `mongodb://localhost:27017`
- **MongoDB Atlas**: `mongodb+srv://username:<EMAIL>/`
- **Custom**: Use your existing connection from Compass

### 4. Project Structure
```
MANGO PROJECT/
├── queries/           # MongoDB queries and aggregations
├── datasets/          # Sample data files
├── playgrounds/       # MongoDB playgrounds (.mongodb files)
├── schemas/           # Collection schemas and validation
└── README.md
```

### 5. MongoDB Playground Files
- Create `.mongodb` files for running queries
- Use MongoDB syntax directly in VS Code
- Execute queries with Ctrl+Shift+R (or Cmd+Shift+R on Mac)

## Next Steps
1. Install the MongoDB extension
2. Set up your MongoDB connection
3. Create your first playground file
4. Import your dataset
5. Start writing queries!
