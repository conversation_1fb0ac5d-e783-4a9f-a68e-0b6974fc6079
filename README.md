# 🥭 MANGO MongoDB Project

A comprehensive MongoDB project demonstrating device data management and query operations.

## 📋 Project Overview

This project contains a MongoDB database with device information (smartphones and tablets) and implements specific queries to demonstrate MongoDB operations including field existence checks and conditional filtering.

## 🗂️ Project Structure

```
MANGO PROJECT/
├── README.md                    # Project documentation
├── ASSIGNMENT_INSTRUCTIONS.md   # Step-by-step assignment guide
├── connection-config.md         # MongoDB connection guide
├── package.json                 # Node.js dependencies
├── .gitignore                  # Git ignore rules
├── datasets/
│   ├── devices_data.json       # Device dataset (5 records)
│   └── README.md               # Dataset documentation
├── queries/
│   ├── sample-queries.mongodb   # General MongoDB examples
│   └── assignment-queries.mongodb # Assignment queries with outputs
└── outputs/
    ├── query-results.md         # Formatted query results
    ├── execution-log.md         # Detailed execution process
    ├── raw-outputs.json         # Machine-readable results
    └── README.md               # Outputs documentation
```

## 🚀 Quick Start

### Prerequisites
- MongoDB installed locally OR MongoDB Atlas account
- MongoDB Compass (recommended)
- VS Code with MongoDB extension (optional)
- Node.js (for dependencies)

### Installation

1. **Clone the repository**
   ```bash
   git clone <your-repo-url>
   cd MANGO-PROJECT
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Import dataset**
   - Open MongoDB Compass
   - Create database: `devices_db`
   - Create collection: `devices`
   - Import `datasets/devices_data.json`

## 🎯 Assignment Queries

This project implements three specific MongoDB queries:

## 📊 Dataset Information

**File**: `datasets/devices_data.json`
- **Records**: 5 devices (IPhone, xTablet, Redmi Note, Samsung, Vivo)
- **Fields**: _id, name, price, releaseDate, spec, color, storage

## 🔍 Query Details

### Query 16: Price Field Existence Check
```javascript
db.devices.find(
  { price: { $exists: true } },
  { name: 1, price: 1, _id: 0 }
);
```
**Expected Result**: All 5 devices with name and price

### Query 17: Profit Field Existence Check
```javascript
db.devices.find({ profit: { $exists: true } });
```
**Expected Result**: Empty array `[]` (no profit field exists)

### Query 18: Price > 699 Filter
```javascript
db.devices.find({ price: { $gt: 699 } });
```
**Expected Result**: 3 devices (IPhone: 799, xTablet: 899, Redmi Note: 899)

## 📊 Query Results & Outputs

This project includes comprehensive output documentation:

- **`outputs/query-results.md`** - Clean, formatted results for each query
- **`outputs/execution-log.md`** - Detailed execution process with performance metrics
- **`outputs/raw-outputs.json`** - Machine-readable JSON format for all results
- **Query files include inline outputs** - See actual results directly in the `.mongodb` files

### Quick Results Summary:
- ✅ **Query 16**: Found 5/5 devices with price field
- ✅ **Query 17**: Correctly returned empty array `[]` for non-existent profit field
- ✅ **Query 18**: Found 3/5 devices with price > 699

## 🛠️ Tools Used

- **MongoDB Compass** - Database management and visualization
- **VS Code MongoDB Extension** - Query development and testing
- **Node.js** - Runtime environment
- **MongoDB Driver** - Database connectivity

## 📝 Usage Instructions

1. **Run Queries in VS Code**:
   - Open `queries/assignment-queries.mongodb`
   - Select query and press `Ctrl+Shift+R`

2. **Run Queries in MongoDB Compass**:
   - Navigate to `devices_db` → `devices`
   - Use the query bar or Aggregations tab

## 🤝 Contributing

Feel free to fork this project and submit pull requests for improvements.

## 📄 License

This project is open source and available under the [MIT License](LICENSE).
