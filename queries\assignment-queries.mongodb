// MongoDB Playground - Assignment Queries
// Use Ctrl+Shift+R (or Cmd+Shift+R on Mac) to run queries

// Select the database to use
use('devices_db');

// ========================================
// QUERY 16: Find Price Field Exists In Document, If Exists Display Name And Price
// ========================================

// Method 1: Using $exists operator
db.devices.find(
  { price: { $exists: true } },
  { name: 1, price: 1, _id: 0 }
);

/* OUTPUT:
[
  { "name": "IPhone", "price": 799 },
  { "name": "xTablet", "price": 899 },
  { "name": "Redmi Note", "price": 899 },
  { "name": "Samsung", "price": 699 },
  { "name": "Vivo", "price": 599 }
]
*/

// Method 2: Alternative using $ne: null
db.devices.find(
  { price: { $ne: null } },
  { name: 1, price: 1, _id: 0 }
);

// ========================================
// QUERY 17: Check If Profit Field Exists, It Does Not Exist, Hence Output Will Be [] Blank
// ========================================

// This will return empty array [] because no documents have 'profit' field
db.devices.find(
  { profit: { $exists: true } }
);

/* OUTPUT:
[]
*/

// Alternative: Show documents that DON'T have profit field (will show all documents)
db.devices.find(
  { profit: { $exists: false } },
  { name: 1, _id: 0 }
);

// ========================================
// QUERY 18: Find price field exists in document and price > 699
// ========================================

// Method 1: Combined condition
db.devices.find({
  $and: [
    { price: { $exists: true } },
    { price: { $gt: 699 } }
  ]
});

// Method 2: Simplified (since $gt already implies existence)
db.devices.find(
  { price: { $gt: 699 } }
);

/* OUTPUT:
[
  {
    "_id": 1,
    "name": "IPhone",
    "price": 799,
    "releaseDate": "2011-05-14T00:00:00Z",
    "spec": { "ram": 4, "screen": 6.5, "cpu": 2.66 },
    "color": ["white", "black"],
    "storage": [64, 128, 256]
  },
  {
    "_id": 2,
    "name": "xTablet",
    "price": 899,
    "releaseDate": "2011-09-01T00:00:00Z",
    "spec": { "ram": 16, "screen": 9.5, "cpu": 3.66 },
    "color": ["white", "black", "purple"],
    "storage": [128, 256, 512]
  },
  {
    "_id": 3,
    "name": "Redmi Note",
    "price": 899,
    "releaseDate": "2015-01-14T00:00:00Z",
    "spec": { "ram": 12, "screen": 9.7, "cpu": 3.66 },
    "color": ["blue"],
    "storage": [16, 64, 128]
  }
]
*/

// Method 3: With projection to show only name and price
db.devices.find(
  { price: { $gt: 699 } },
  { name: 1, price: 1, _id: 0 }
);

// ========================================
// ADDITIONAL USEFUL QUERIES
// ========================================

// Count documents where price exists
db.devices.countDocuments({ price: { $exists: true } });

// Count documents where profit exists (should be 0)
db.devices.countDocuments({ profit: { $exists: true } });

// Find all devices with price > 699 and show all fields
db.devices.find({ price: { $gt: 699 } }).pretty();
