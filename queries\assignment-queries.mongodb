// MongoDB Playground - Assignment Queries
// Use Ctrl+Shift+R (or Cmd+Shift+R on Mac) to run queries

// Select the database to use
use('devices_db');

// ========================================
// QUERY 16: Find Price Field Exists In Document, If Exists Display Name And Price
// ========================================

// Method 1: Using $exists operator
db.devices.find(
  { price: { $exists: true } },
  { name: 1, price: 1, _id: 0 }
);

// Method 2: Alternative using $ne: null
db.devices.find(
  { price: { $ne: null } },
  { name: 1, price: 1, _id: 0 }
);

// ========================================
// QUERY 17: Check If Profit Field Exists, It Does Not Exist, Hence Output Will Be [] Blank
// ========================================

// This will return empty array [] because no documents have 'profit' field
db.devices.find(
  { profit: { $exists: true } }
);

// Alternative: Show documents that DON'T have profit field (will show all documents)
db.devices.find(
  { profit: { $exists: false } },
  { name: 1, _id: 0 }
);

// ========================================
// QUERY 18: Find price field exists in document and price > 699
// ========================================

// Method 1: Combined condition
db.devices.find({
  $and: [
    { price: { $exists: true } },
    { price: { $gt: 699 } }
  ]
});

// Method 2: Simplified (since $gt already implies existence)
db.devices.find(
  { price: { $gt: 699 } }
);

// Method 3: With projection to show only name and price
db.devices.find(
  { price: { $gt: 699 } },
  { name: 1, price: 1, _id: 0 }
);

// ========================================
// ADDITIONAL USEFUL QUERIES
// ========================================

// Count documents where price exists
db.devices.countDocuments({ price: { $exists: true } });

// Count documents where profit exists (should be 0)
db.devices.countDocuments({ profit: { $exists: true } });

// Find all devices with price > 699 and show all fields
db.devices.find({ price: { $gt: 699 } }).pretty();
